/**
 * Simple Momentum Strategy - Generates immediate signals with minimal data
 * This strategy can produce BUY/SELL signals with just 2 candles
 */

export function evaluateSimpleMomentum(candles: Candle[], config: SimpleMomentumConfig, lastSignal: Signal): SignalMeta {
	if (candles.length < 2) {
		return { signal: undefined, reason: 'Need at least 2 candles for momentum', confidence: 'low' }
	}

	const current = candles[candles.length - 1]!
	const previous = candles[candles.length - 2]!

	// Calculate price change
	const priceChange = current.close - previous.close
	const percentChange = (priceChange / previous.close) * 100

	// Calculate volume-like indicator using candle body size
	const currentBody = Math.abs(current.close - current.open)
	const previousBody = Math.abs(previous.close - previous.open)
	const bodyRatio = currentBody / previousBody

	// Determine confidence based on price change magnitude and body size
	let confidence: SignalMeta['confidence'] = 'low'
	if (Math.abs(percentChange) > config.strongThreshold && bodyRatio > 1.2) {
		confidence = 'high'
	} else if (Math.abs(percentChange) > config.mediumThreshold) {
		confidence = 'medium'
	}

	// Generate signals based on momentum
	if (priceChange > 0 && Math.abs(percentChange) > config.minThreshold) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `Upward momentum: +${percentChange.toFixed(4)}% (body ratio: ${bodyRatio.toFixed(2)})`,
				confidence
			}
		}
	}

	if (priceChange < 0 && Math.abs(percentChange) > config.minThreshold) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `Downward momentum: ${percentChange.toFixed(4)}% (body ratio: ${bodyRatio.toFixed(2)})`,
				confidence
			}
		}
	}

	return { 
		signal: 'HOLD', 
		reason: `Weak momentum: ${percentChange.toFixed(4)}% (threshold: ${config.minThreshold}%)`, 
		confidence: 'low' 
	}
}

// Configuration interface for Simple Momentum strategy
export interface SimpleMomentumConfig {
	minThreshold: number      // Minimum percentage change to trigger signal
	mediumThreshold: number   // Threshold for medium confidence
	strongThreshold: number   // Threshold for high confidence
}
