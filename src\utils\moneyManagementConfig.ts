/**
 * Money Management Configuration Utilities
 * Provides validation, default settings, and configuration management for trading parameters
 */

/**
 * Default money management configuration templates
 */
export const DEFAULT_MONEY_MANAGEMENT_CONFIGS = {
	conservative: {
		baseStake: 10,
		maxStakePercent: 2, // 2% of balance max
		martingaleStrategy: 'fixed' as MartingaleStrategy,
		martingaleFactor: 1.5,
		maxMartingaleSteps: 3,
		martingaleResetOnWin: true,
		targetProfit: 100,
		maxLoss: 50,
		maxTrades: 50,
		minCapitalRequired: 100,
		maxRiskPerTrade: 20,
		capitalPreservationMode: true,
		emergencyStopLoss: 75
	},
	
	moderate: {
		baseStake: 10,
		maxStakePercent: 5, // 5% of balance max
		martingaleStrategy: 'fixed' as MartingaleStrategy,
		martingaleFactor: 2.0,
		maxMartingaleSteps: 5,
		martingaleResetOnWin: true,
		targetProfit: 200,
		maxLoss: 100,
		maxTrades: 100,
		minCapitalRequired: 200,
		maxRiskPerTrade: 50,
		capitalPreservationMode: false,
		emergencyStopLoss: 150
	},
	
	aggressive: {
		baseStake: 20,
		maxStakePercent: 10, // 10% of balance max
		martingaleStrategy: 'progressive' as MartingaleStrategy,
		martingaleFactor: 2.5,
		maxMartingaleSteps: 7,
		martingaleResetOnWin: true,
		targetProfit: 500,
		maxLoss: 250,
		maxTrades: 200,
		minCapitalRequired: 500,
		maxRiskPerTrade: 100,
		capitalPreservationMode: false,
		emergencyStopLoss: 300
	},
	
	fibonacci: {
		baseStake: 10,
		maxStakePercent: 8, // 8% of balance max
		martingaleStrategy: 'fibonacci' as MartingaleStrategy,
		martingaleFactor: 1.0, // Not used in fibonacci
		maxMartingaleSteps: 10,
		martingaleResetOnWin: true,
		targetProfit: 300,
		maxLoss: 150,
		maxTrades: 150,
		minCapitalRequired: 300,
		maxRiskPerTrade: 75,
		capitalPreservationMode: false,
		emergencyStopLoss: 200
	}
} as const

/**
 * Configuration validation rules
 */
export const VALIDATION_RULES = {
	baseStake: { min: 1, max: 1000 },
	maxStakePercent: { min: 0.1, max: 50 },
	martingaleFactor: { min: 1.1, max: 10 },
	maxMartingaleSteps: { min: 1, max: 20 },
	targetProfit: { min: 1, max: 10000 },
	maxLoss: { min: 1, max: 5000 },
	maxTrades: { min: 1, max: 1000 },
	minCapitalRequired: { min: 10, max: 10000 },
	maxRiskPerTrade: { min: 1, max: 1000 },
	emergencyStopLoss: { min: 1, max: 5000 }
} as const

/**
 * Validation error types
 */
export interface ValidationError {
	field: string
	value: any
	message: string
	rule: string
}

/**
 * Validation result
 */
export interface ValidationResult {
	isValid: boolean
	errors: ValidationError[]
	warnings: string[]
}

/**
 * Validate money management configuration
 */
export function validateMoneyManagementConfig(config: Partial<MoneyManagementConfig>): ValidationResult {
	const errors: ValidationError[] = []
	const warnings: string[] = []

	// Validate numeric fields with bounds
	for (const [field, rules] of Object.entries(VALIDATION_RULES)) {
		const value = (config as any)[field]
		
		if (value !== undefined) {
			if (typeof value !== 'number' || isNaN(value)) {
				errors.push({
					field,
					value,
					message: `${field} must be a valid number`,
					rule: 'type'
				})
				continue
			}

			if (value < rules.min) {
				errors.push({
					field,
					value,
					message: `${field} must be at least ${rules.min}`,
					rule: 'min'
				})
			}

			if (value > rules.max) {
				errors.push({
					field,
					value,
					message: `${field} must not exceed ${rules.max}`,
					rule: 'max'
				})
			}
		}
	}

	// Validate martingale strategy
	if (config.martingaleStrategy) {
		const validStrategies: MartingaleStrategy[] = ['fixed', 'progressive', 'fibonacci', 'custom']
		if (!validStrategies.includes(config.martingaleStrategy)) {
			errors.push({
				field: 'martingaleStrategy',
				value: config.martingaleStrategy,
				message: `martingaleStrategy must be one of: ${validStrategies.join(', ')}`,
				rule: 'enum'
			})
		}
	}

	// Cross-field validations
	if (config.maxLoss && config.emergencyStopLoss && config.maxLoss > config.emergencyStopLoss) {
		warnings.push('maxLoss is greater than emergencyStopLoss - emergency stop may not be effective')
	}

	if (config.baseStake && config.maxRiskPerTrade && config.baseStake > config.maxRiskPerTrade) {
		errors.push({
			field: 'baseStake',
			value: config.baseStake,
			message: 'baseStake cannot be greater than maxRiskPerTrade',
			rule: 'cross-field'
		})
	}

	if (config.targetProfit && config.maxLoss && config.targetProfit < config.maxLoss) {
		warnings.push('targetProfit is less than maxLoss - consider adjusting risk/reward ratio')
	}

	// Risk level warnings
	if (config.maxStakePercent && config.maxStakePercent > 10) {
		warnings.push('maxStakePercent above 10% is considered high risk')
	}

	if (config.martingaleFactor && config.martingaleFactor > 3) {
		warnings.push('martingaleFactor above 3.0 can lead to rapid capital depletion')
	}

	if (config.maxMartingaleSteps && config.maxMartingaleSteps > 10) {
		warnings.push('maxMartingaleSteps above 10 is extremely risky')
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings
	}
}

/**
 * Get default configuration by risk profile
 */
export function getDefaultConfig(profile: keyof typeof DEFAULT_MONEY_MANAGEMENT_CONFIGS): MoneyManagementConfig {
	const template = DEFAULT_MONEY_MANAGEMENT_CONFIGS[profile]
	if (!template) {
		throw new Error(`Unknown risk profile: ${profile}`)
	}
	
	return { ...template }
}

/**
 * Merge configuration with defaults and validate
 */
export function createValidatedConfig(
	baseConfig: Partial<MoneyManagementConfig>,
	profile: keyof typeof DEFAULT_MONEY_MANAGEMENT_CONFIGS = 'moderate'
): { config: MoneyManagementConfig; validation: ValidationResult } {
	const defaultConfig = getDefaultConfig(profile)
	const mergedConfig = { ...defaultConfig, ...baseConfig }
	
	const validation = validateMoneyManagementConfig(mergedConfig)
	
	if (!validation.isValid) {
		throw new Error(`Configuration validation failed: ${validation.errors.map(e => e.message).join(', ')}`)
	}
	
	return {
		config: mergedConfig,
		validation
	}
}

/**
 * Calculate recommended configuration based on account balance
 */
export function calculateRecommendedConfig(accountBalance: number): MoneyManagementConfig {
	let profile: keyof typeof DEFAULT_MONEY_MANAGEMENT_CONFIGS
	let baseStake: number
	
	if (accountBalance < 100) {
		profile = 'conservative'
		baseStake = Math.max(1, accountBalance * 0.02) // 2% of balance
	} else if (accountBalance < 500) {
		profile = 'conservative'
		baseStake = Math.max(5, accountBalance * 0.03) // 3% of balance
	} else if (accountBalance < 1000) {
		profile = 'moderate'
		baseStake = Math.max(10, accountBalance * 0.02) // 2% of balance
	} else if (accountBalance < 5000) {
		profile = 'moderate'
		baseStake = Math.max(20, accountBalance * 0.015) // 1.5% of balance
	} else {
		profile = 'aggressive'
		baseStake = Math.max(50, accountBalance * 0.01) // 1% of balance
	}
	
	const config = getDefaultConfig(profile)
	config.baseStake = Math.round(baseStake)
	config.minCapitalRequired = Math.max(config.minCapitalRequired, accountBalance * 0.1)
	config.maxRiskPerTrade = Math.max(config.maxRiskPerTrade, baseStake * 5)
	config.targetProfit = Math.max(config.targetProfit, accountBalance * 0.2)
	config.maxLoss = Math.max(config.maxLoss, accountBalance * 0.1)
	config.emergencyStopLoss = Math.max(config.emergencyStopLoss, accountBalance * 0.15)
	
	return config
}

/**
 * Export configuration to JSON string
 */
export function exportConfig(config: MoneyManagementConfig): string {
	return JSON.stringify(config, null, 2)
}

/**
 * Import configuration from JSON string with validation
 */
export function importConfig(jsonString: string): MoneyManagementConfig {
	try {
		const config = JSON.parse(jsonString) as Partial<MoneyManagementConfig>
		const validation = validateMoneyManagementConfig(config)
		
		if (!validation.isValid) {
			throw new Error(`Invalid configuration: ${validation.errors.map(e => e.message).join(', ')}`)
		}
		
		return config as MoneyManagementConfig
	} catch (error) {
		if (error instanceof SyntaxError) {
			throw new Error('Invalid JSON format')
		}
		throw error
	}
}
