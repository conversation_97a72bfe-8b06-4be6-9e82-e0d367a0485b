import { processHistoryFastData } from '../utils/processor'

/**
 * Simple test suite for processHistoryFastData function
 * Note: These are basic tests using simple assertions
 */

const mockHistoryFastData: HistoryFastData = {
	asset: 'EURUSD',
	index: 123456,
	data: [
		{
			symbol_id: 'EURUSD',
			time: 1000,
			open: 1.1,
			high: 1.105,
			low: 1.095,
			close: 1.102
		},
		{
			symbol_id: 'EURUSD',
			time: 1030,
			open: 1.102,
			high: 1.108,
			low: 1.1,
			close: 1.106
		},
		{
			symbol_id: 'EURUSD',
			time: 1060,
			open: 1.106,
			high: 1.11,
			low: 1.104,
			close: 1.109
		},
		{
			symbol_id: 'EURUSD',
			time: 1090,
			open: 1.109,
			high: 1.112,
			low: 1.107,
			close: 1.111
		}
	]
}

function assert(condition: boolean, message: string) {
	if (!condition) {
		throw new Error(`Assertion failed: ${message}`)
	}
}

function assertEqual<T>(actual: T, expected: T, message: string) {
	if (JSON.stringify(actual) !== JSON.stringify(expected)) {
		throw new Error(
			`Assertion failed: ${message}\nExpected: ${JSON.stringify(expected)}\nActual: ${JSON.stringify(actual)}`
		)
	}
}

async function testEmptyData() {
	console.log('Test 1: Empty data handling')

	const emptyData: HistoryFastData = {
		asset: 'EURUSD',
		index: 123456,
		data: []
	}

	const result = processHistoryFastData(emptyData, 60)
	assertEqual(result, [], 'Should return empty array for empty data')
	console.log('✅ Empty data test passed\n')
}

async function testNullUndefinedData() {
	console.log('Test 2: Null/undefined data handling')

	const result1 = processHistoryFastData(null as any, 60)
	const result2 = processHistoryFastData(undefined as any, 60)

	assertEqual(result1, [], 'Should return empty array for null data')
	assertEqual(result2, [], 'Should return empty array for undefined data')
	console.log('✅ Null/undefined data test passed\n')
}

async function testInvalidPeriod() {
	console.log('Test 3: Invalid period handling')

	try {
		processHistoryFastData(mockHistoryFastData, 0)
		throw new Error('Should have thrown error for zero period')
	} catch (error) {
		assert(
			error instanceof Error && error.message === 'Period must be a positive number',
			'Should throw correct error for zero period'
		)
	}

	try {
		processHistoryFastData(mockHistoryFastData, -30)
		throw new Error('Should have thrown error for negative period')
	} catch (error) {
		assert(
			error instanceof Error && error.message === 'Period must be a positive number',
			'Should throw correct error for negative period'
		)
	}

	console.log('✅ Invalid period test passed\n')
}

async function testSamePeriodProcessing() {
	console.log('Test 4: Same period processing (no aggregation)')

	const result = processHistoryFastData(mockHistoryFastData, 30)

	// Should have 3 candles (4 input - 1 removed as incomplete)
	assert(result.length === 3, `Should have 3 candles, got ${result.length}`)

	// First candle should have rounded time (1000 / 30 = 33.33, floor(33.33) * 30 = 990)
	const expectedFirst = {
		symbol_id: 'EURUSD',
		time: 990,
		open: 1.1,
		high: 1.105,
		low: 1.095,
		close: 1.102
	}
	assertEqual(result[0], expectedFirst, 'First candle should have rounded time and correct OHLC')

	console.log('✅ Same period processing test passed\n')
}

async function testCandleAggregation() {
	console.log('Test 5: Candle aggregation for larger period')

	const result = processHistoryFastData(mockHistoryFastData, 60)

	// Let's calculate the expected groupings:
	// 1000 / 60 = 16.67, floor(16.67) * 60 = 960
	// 1030 / 60 = 17.17, floor(17.17) * 60 = 1020
	// 1060 / 60 = 17.67, floor(17.67) * 60 = 1020
	// 1090 / 60 = 18.17, floor(18.17) * 60 = 1080
	// So we have groups: 960 (1 candle), 1020 (2 candles), 1080 (1 candle)
	// After removing last candle, we should have 2 candles

	assert(result.length === 2, `Should have 2 candles, got ${result.length}`)

	// First group (960) - only one candle
	const expectedFirst = {
		symbol_id: 'EURUSD',
		time: 960,
		open: 1.1,
		high: 1.105,
		low: 1.095,
		close: 1.102
	}
	assertEqual(result[0], expectedFirst, 'First aggregated candle should have correct OHLC values')

	// Second group (1020) - two candles aggregated
	const expectedSecond = {
		symbol_id: 'EURUSD',
		time: 1020,
		open: 1.102, // First candle in group (time 1030)
		high: 1.11, // Max of 1.108 and 1.11
		low: 1.1, // Min of 1.1 and 1.104
		close: 1.109 // Last candle in group (time 1060)
	}
	assertEqual(result[1], expectedSecond, 'Second aggregated candle should have correct OHLC values')

	console.log('✅ Candle aggregation test passed\n')
}

async function testSingleCandle() {
	console.log('Test 6: Single candle handling')

	const singleCandleData: HistoryFastData = {
		asset: 'EURUSD',
		index: 123456,
		data: [
			{
				symbol_id: 'EURUSD',
				time: 1000,
				open: 1.1,
				high: 1.105,
				low: 1.095,
				close: 1.102
			}
		]
	}

	const result = processHistoryFastData(singleCandleData, 30)

	// Should return empty array as the single candle is removed (incomplete)
	assert(result.length === 0, `Should have 0 candles for single input, got ${result.length}`)

	console.log('✅ Single candle test passed\n')
}

async function testSorting() {
	console.log('Test 7: Candle sorting')

	const unsortedData: HistoryFastData = {
		asset: 'EURUSD',
		index: 123456,
		data: [
			{
				symbol_id: 'EURUSD',
				time: 1060,
				open: 1.106,
				high: 1.11,
				low: 1.104,
				close: 1.109
			},
			{
				symbol_id: 'EURUSD',
				time: 1000,
				open: 1.1,
				high: 1.105,
				low: 1.095,
				close: 1.102
			},
			{
				symbol_id: 'EURUSD',
				time: 1030,
				open: 1.102,
				high: 1.108,
				low: 1.1,
				close: 1.106
			}
		]
	}

	const result = processHistoryFastData(unsortedData, 30)

	// Should be sorted by time (with rounded timestamps)
	// 1000 / 30 = 33.33, floor(33.33) * 30 = 990
	// 1030 / 30 = 34.33, floor(34.33) * 30 = 1020
	assert(result[0].time === 990, `First candle time should be 990, got ${result[0].time}`)
	assert(result[1].time === 1020, `Second candle time should be 1020, got ${result[1].time}`)

	console.log('✅ Sorting test passed\n')
}

// Run all tests
async function runProcessorTests() {
	console.log('🚀 Starting processHistoryFastData Test Suite\n')
	console.log('='.repeat(50))

	try {
		await testEmptyData()
		await testNullUndefinedData()
		await testInvalidPeriod()
		await testSamePeriodProcessing()
		await testCandleAggregation()
		await testSingleCandle()
		await testSorting()

		console.log('='.repeat(50))
		console.log('🎉 All processHistoryFastData tests passed!')
	} catch (error) {
		console.error('❌ Test failed:', error)
		process.exit(1)
	}
}

// Export for potential use in other test files
export { runProcessorTests }

// Run tests if this file is executed directly
if (import.meta.main) {
	runProcessorTests().catch(console.error)
}
