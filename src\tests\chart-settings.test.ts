import { PocketOptionApi } from '../api'
import { PocketOption } from '../broker/PocketOption'
import { logger } from '../utils/logger'

/**
 * Test suite specifically for chart settings functionality
 */

// Mock environment variables for testing
process.env.SSID = process.env.SSID || '42["auth",{"session":"test","uid":"test"}]'
process.env.DEMO = process.env.DEMO || 'true'

async function testChartSettingsEndpoint() {
	console.log('🧪 Testing Chart Settings Endpoint...\n')

	try {
		// Create broker instance
		const broker = new PocketOption(process.env.SSID as string, true)
		const api = new PocketOptionApi(broker)

		// Test 1: Check chart settings without connection
		console.log('Test 1: Get chart settings without connection')
		try {
			await api.getChartSettings()
			console.log('Unexpected success ❌')
		} catch (error) {
			console.log('Expected error:', error instanceof Error ? error.message : error)
			console.log('Error handling working ✅\n')
		}

		// Test 2: Test connection and chart settings retrieval
		console.log('Test 2: Connection and chart settings retrieval')
		try {
			await api.connect()
			console.log('Connected successfully ✅')

			// Try to get chart settings with a short timeout for testing
			const chartSettings = await api.getChartSettings(2000)
			if (chartSettings) {
				console.log('Chart settings retrieved successfully ✅')
				console.log('Chart settings structure:', Object.keys(chartSettings))
			} else {
				console.log('Chart settings not available (expected with test credentials) ⚠️')
			}
		} catch (error) {
			console.log('Connection/chart settings failed (expected with test credentials):', error instanceof Error ? error.message : error)
			console.log('Error handling working ✅\n')
		}

		// Test 3: Test direct broker chart settings methods
		console.log('Test 3: Direct broker chart settings methods')
		const directSettings = broker.getChartSettings()
		console.log('Direct chart settings:', directSettings ? 'Available' : 'Not available')

		try {
			await broker.requestChartSettings()
			console.log('Chart settings request sent ✅')
		} catch (error) {
			console.log('Chart settings request failed (expected without connection):', error instanceof Error ? error.message : error)
		}

		console.log('🎉 Chart settings tests completed!\n')
	} catch (error) {
		console.error('❌ Chart settings test suite failed:', error)
	}
}

async function testChartSettingsDataParsing() {
	console.log('🧪 Testing Chart Settings Data Parsing...\n')

	try {
		const broker = new PocketOption(process.env.SSID as string, true)

		// Test different data structures that might be received
		const testData = [
			// Test case 1: Object with settings as object
			[{
				chart_id: 'test-chart',
				settings: {
					chartId: 'test-chart',
					chartType: 1,
					chartPeriod: 30,
					symbol: 'USDARS_otc',
					candlesTimer: true,
					demoDealAmount: 100,
					liveDealAmount: 100,
					enabledTradeMonitor: true,
					enabledRatingWidget: false,
					isVisible: true,
					fastTimeframe: 30,
					enabledAutoscroll: true,
					enabledGridSnap: false,
					minimizedTradePanel: false,
					fastCloseAt: 0,
					enableQuickAutoOffset: false,
					quickAutoOffsetValue: 0,
					showArea: false,
					percentAmount: 0
				}
			}],
			// Test case 2: Object with settings as JSON string
			[{
				chart_id: 'test-chart',
				settings: JSON.stringify({
					chartId: 'test-chart',
					chartType: 1,
					chartPeriod: 30,
					symbol: 'USDARS_otc'
				})
			}]
		]

		console.log('Testing chart settings data parsing with different formats...')
		
		// Note: We can't directly test the private handleChartSettings method,
		// but we can verify the public methods work correctly
		const initialSettings = broker.getChartSettings()
		console.log('Initial chart settings:', initialSettings ? 'Available' : 'Not available')

		console.log('🎉 Chart settings data parsing tests completed!\n')
	} catch (error) {
		console.error('❌ Chart settings data parsing test failed:', error)
	}
}

// Run tests
async function runChartSettingsTests() {
	console.log('🚀 Starting Chart Settings Test Suite\n')
	console.log('='.repeat(60))

	await testChartSettingsEndpoint()
	console.log('='.repeat(60))

	await testChartSettingsDataParsing()
	console.log('='.repeat(60))

	console.log('✨ Chart settings test suite completed!')
}

// Export for potential use in other test files
export { testChartSettingsEndpoint, testChartSettingsDataParsing }

// Run tests if this file is executed directly
if (import.meta.main) {
	runChartSettingsTests().catch(console.error)
}
