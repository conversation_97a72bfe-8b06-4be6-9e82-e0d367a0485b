export function processHistoryData(data: RawHistoryData, period: number) {
	if (!data || !data.history || data.history.length === 0) return []

	const candleGroup = new Map<number, number[]>()

	for (const [timestamp, price] of data.history) {
		const roundedTimestamp = Math.floor(timestamp / period) * period
		if (!candleGroup.has(roundedTimestamp)) {
			candleGroup.set(roundedTimestamp, [])
		}
		candleGroup.get(roundedTimestamp)!.push(price)
	}

	const ohlcCandles: Candle[] = []

	for (const [time, prices] of candleGroup.entries()) {
		if (prices.length > 0) {
			ohlcCandles.push({
				time,
				open: prices[0]!,
				high: Math.max(...prices),
				low: Math.min(...prices),
				close: prices[prices.length - 1]!
			})
		}
	}

	// Sort the candles by time
	ohlcCandles.sort((a, b) => a.time - b.time)

	// Remove the last candle as it is not complete
	if (ohlcCandles.length > 0) {
		ohlcCandles.pop()
	}

	return ohlcCandles
}

export function processHistoryFastData(data: HistoryFastData, period: number): Candle[] {
	// Input validation
	if (!data || !data.data || data.data.length === 0) {
		return []
	}

	if (period <= 0) {
		throw new Error('Period must be a positive number')
	}

	const inputCandles: HistoryCandle[] = data.data

	// Sort candles by time to ensure proper processing
	inputCandles.sort((a, b) => a.time - b.time)

	// Group candles by the specified period
	const candleGroups = new Map<number, HistoryCandle[]>()

	for (const candle of inputCandles) {
		// Round timestamp down to the nearest period boundary
		const roundedTimestamp = Math.floor(candle.time / period) * period

		if (!candleGroups.has(roundedTimestamp)) {
			candleGroups.set(roundedTimestamp, [])
		}
		candleGroups.get(roundedTimestamp)!.push(candle)
	}

	// Convert grouped candles to OHLC format
	const processedCandles: Candle[] = []

	for (const [time, candles] of candleGroups.entries()) {
		if (candles.length === 0) continue

		// Sort candles within the group by time
		candles.sort((a, b) => a.time - b.time)

		// Calculate OHLC values from the grouped candles
		const firstCandle = candles[0]!
		const lastCandle = candles[candles.length - 1]!

		const open = firstCandle.open
		const close = lastCandle.close
		const high = Math.max(...candles.map(c => c.high))
		const low = Math.min(...candles.map(c => c.low))

		// Use the first candle's symbol_id for consistency
		// const symbol_id = firstCandle.symbol_id

		processedCandles.push({
			time,
			open,
			high,
			low,
			close
		})
	}

	// Sort the final candles by time
	processedCandles.sort((a, b) => a.time - b.time)

	// Remove the last candle as it might not be complete
	// (following the pattern from processHistoryData)
	if (processedCandles.length > 0) {
		processedCandles.pop()
	}

	return processedCandles
}
