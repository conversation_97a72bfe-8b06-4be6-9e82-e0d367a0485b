interface TradeOutcome {
	signal: Signal
	result: 'win' | 'loss'
}

// Enhanced trade record for detailed tracking
interface TradeRecord {
	id: string
	timestamp: number
	signal: Signal
	stake: number
	result: 'win' | 'loss' | 'pending'
	profit: number
	loss: number
	martingaleStep: number
	balanceBefore: number
	balanceAfter: number
	reason: string
	confidence: 'low' | 'medium' | 'high'
}

// Martingale strategy types
type MartingaleStrategy = 'fixed' | 'progressive' | 'fibonacci' | 'custom'

// Session termination reasons
type SessionTerminationReason =
	| 'target_profit_reached'
	| 'max_loss_reached'
	| 'capital_depleted'
	| 'max_trades_reached'
	| 'manual_stop'
	| 'broker_error'
	| 'insufficient_balance'
	| 'session_active'

// Trading session state
type TradingSessionState = 'ACTIVE' | 'PAUSED' | 'TERMINATED' | 'INITIALIZING'

// Money management configuration
interface MoneyManagementConfig {
	// Basic stake settings
	baseStake: number
	maxStakePercent: number // Maximum percentage of balance to risk per trade

	// Martingale settings
	martingaleStrategy: MartingaleStrategy
	martingaleFactor: number
	maxMartingaleSteps: number
	martingaleResetOnWin: boolean

	// Session limits
	targetProfit?: number
	maxLoss?: number
	maxTrades?: number
	minCapitalRequired: number

	// Risk management
	maxRiskPerTrade: number // Maximum amount to risk per trade
	capitalPreservationMode: boolean // Conservative mode when balance is low
	emergencyStopLoss: number // Emergency stop if losses exceed this amount
}

// Enhanced trading session with comprehensive tracking
interface TradingSession {
	// Basic session info
	id: string
	startTime: number
	endTime?: number
	state: TradingSessionState
	terminationReason?: SessionTerminationReason

	// Current state
	steps: number
	lastTradeTime: number
	currentStake: number

	// Balance tracking
	initialBalance: number
	currentBalance: number
	minBalance: number
	maxBalance: number

	// Trade statistics
	totalProfit: number
	totalLoss: number
	totalWins: number
	totalTrades: number
	winStreak: number
	lossStreak: number
	maxWinStreak: number
	maxLossStreak: number

	// Trade records
	trades: TradeRecord[]
	largestWin: number
	largestLoss: number

	// Performance metrics
	winRate: number
	profitFactor: number
	averageWin: number
	averageLoss: number
	expectancy: number
}

// Session summary for reporting
interface SessionSummary {
	session: TradingSession
	performance: {
		totalReturn: number
		totalReturnPercent: number
		winRate: number
		profitFactor: number
		sharpeRatio?: number
		maxDrawdown: number
		maxDrawdownPercent: number
		averageTradeReturn: number
		averageWin: number
		averageLoss: number
		expectancy: number
		largestWin: number
		largestLoss: number
		consecutiveWins: number
		consecutiveLosses: number
	}
	statistics: {
		totalTrades: number
		winningTrades: number
		losingTrades: number
		duration: number
		tradesPerHour: number
		averageTradeTime: number
	}
	termination: {
		reason: SessionTerminationReason
		timestamp: number
		finalBalance: number
		message: string
	}
}

// Legacy interfaces for backward compatibility
interface TradeSession {
	steps: number
	lastTradeTime: number
	currentStake: number
	totalProfit: number
	totalLoss: number
	totalWins: number
	totalTrades: number
}

// Enhanced TradeManager options
interface TradeManagerOptions {
	// Basic settings
	baseStake: number
	martingaleFactor: number
	expiry: string
	cooldownMs: number
	maxSteps: number
	asset: string
	directionMap?: Record<Signal, 'call' | 'put'>
	placeOrder: (payload: OrderPayload) => Promise<void>

	// Legacy compatibility
	maxLoss?: number
	stopWin?: number

	// Enhanced money management
	moneyManagement?: MoneyManagementConfig

	// Balance integration
	getBalance?: () => Promise<number>

	// Session settings
	sessionId?: string
	enableDetailedTracking?: boolean
	enableSessionReporting?: boolean
}
