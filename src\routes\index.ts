import { Router } from 'express'
import { PocketOptionApi } from '../api'
import { PocketOption } from '../broker/PocketOption'
import { logger } from '../utils/logger'

const router = Router()

// Create broker instance (will be initialized when routes are used)
let api: PocketOptionApi | null

// Initialize API function
const initializeApi = async (): Promise<PocketOptionApi> => {
	return new Promise((resolve, reject) => {
		if (api && api.isApiConnected()) {
			return resolve(api)
		}

		if (!api) {
			const ssID = process.env.SSID
			if (!ssID) {
				reject(`SSID environment variable is required`)
			} else {
				const demo = process.env.DEMO === 'true'
				const broker = new PocketOption(ssID, demo)
				api = new PocketOptionApi(broker)

				// Initialize connection
				api
					.connect()
					.then(() => {
						if (api) {
							resolve(api)
						} else {
							reject(new Error('API instance is null after connection'))
						}
					})
					.catch(error => {
						logger.error('Routes', 'Failed to initialize API connection', error)
						reject(error)
					})
			}
		}
	})
}

const getCandles = async (_req: any, res: any) => {
	try {
		const apiInstance = await initializeApi()

		if (!apiInstance.isApiConnected()) {
			return res.status(503).json({
				error: 'API not connected to broker',
				message: 'Please try again later'
			})
		}

		const chartSettings = await apiInstance.getChartSettings()
		if (!chartSettings) {
			return res.status(404).json({
				error: 'Chart settings not available',
				message: 'Chart settings have not been received from the broker yet. Try again in a few seconds.'
			})
		}

		const candles = await apiInstance.getCandles(chartSettings.symbol)

		res.json({
			success: true,
			candles,
			timestamp: new Date().toISOString()
		})
	} catch (error) {
		logger.error('Routes', 'Error getting candles', error)
		res.status(500).json({
			error: 'Failed to get candles',
			message: error instanceof Error ? error.message : 'Unknown error'
		})
	}
}

const selectAsset = async (_req: any, res: any) => {
	try {
		const apiInstance = await initializeApi()

		if (!apiInstance.isApiConnected()) {
			return res.status(503).json({
				error: 'API not connected to broker',
				message: 'Please try again later'
			})
		}

		const chartSettings = await apiInstance.getChartSettings()
		if (!chartSettings) {
			return res.status(404).json({
				error: 'Chart settings not available',
				message: 'Chart settings have not been received from the broker yet. Try again in a few seconds.'
			})
		}

		await apiInstance.selectAsset(chartSettings.symbol, chartSettings.chartPeriod)

		res.json({
			success: true,
			asset: chartSettings.symbol,
			chartPeriod: chartSettings.chartPeriod,
			timestamp: new Date().toISOString()
		})
	} catch (error) {
		logger.error('Routes', 'Error selecting asset', error)
		res.status(500).json({
			error: 'Failed to select asset',
			message: error instanceof Error ? error.message : 'Unknown error'
		})
	}
}

/**
 * Get account balance
 */
const getBalance = async (_req: any, res: any) => {
	try {
		const apiInstance = await initializeApi()

		if (!apiInstance.isApiConnected()) {
			return res.status(503).json({
				error: 'API not connected to broker',
				message: 'Please try again later'
			})
		}

		const balance = await apiInstance.getBalance()
		res.json({
			success: true,
			balance,
			timestamp: new Date().toISOString()
		})
	} catch (error) {
		logger.error('Routes', 'Error getting balance', error)
		res.status(500).json({
			error: 'Failed to get balance',
			message: error instanceof Error ? error.message : 'Unknown error'
		})
	}
}

const placeOrder = async (req: any, res: any) => {
	try {
		const apiInstance = await initializeApi()

		if (!apiInstance.isApiConnected()) {
			return res.status(503).json({
				error: 'API not connected to broker',
				message: 'Please try again later'
			})
		}

		const { asset, action, amount, time, optionType } = req.body

		await apiInstance.placeOrder({
			asset,
			action,
			amount,
			time,
			optionType,
			isDemo: 1
		})

		res.json({
			success: true,
			timestamp: new Date().toISOString()
		})
	} catch (error) {
		logger.error('Routes', 'Error placing order', error)
		res.status(500).json({
			error: 'Failed to place order',
			message: error instanceof Error ? error.message : 'Unknown error'
		})
	}
}

/**
 * Get API connection status
 */
const getStatus = async (_req: any, res: any) => {
	try {
		const apiInstance = await initializeApi()
		res.json({
			connected: apiInstance.isApiConnected(),
			timestamp: new Date().toISOString()
		})
	} catch (error) {
		logger.error('Routes', 'Error getting status', error)
		res.status(500).json({
			error: 'Failed to get status',
			message: error instanceof Error ? error.message : 'Unknown error'
		})
	}
}

const getChartSettings = async (_req: any, res: any) => {
	try {
		const apiInstance = await initializeApi()

		if (!apiInstance.isApiConnected()) {
			return res.status(503).json({
				error: 'API not connected to broker',
				message: 'Please try again later'
			})
		}

		const chartSettings = await apiInstance.getChartSettings()

		if (!chartSettings) {
			return res.status(404).json({
				error: 'Chart settings not available',
				message: 'Chart settings have not been received from the broker yet. Try again in a few seconds.'
			})
		}

		res.json({
			success: true,
			chartSettings,
			timestamp: new Date().toISOString()
		})
	} catch (error) {
		logger.error('Routes', 'Error getting chart settings', error)
		res.status(500).json({
			error: 'Failed to get chart settings',
			message: error instanceof Error ? error.message : 'Unknown error'
		})
	}
}

/**
 * Health check endpoint
 */
const getHealth = async (_req: any, res: any) => {
	try {
		const apiInstance = await initializeApi()
		res.json({
			status: 'ok',
			service: 'pocket-option-api',
			timestamp: new Date().toISOString(),
			broker_connected: apiInstance.isApiConnected()
		})
	} catch (error) {
		logger.error('Routes', 'Error in health check', error)
		res.status(500).json({
			status: 'error',
			service: 'pocket-option-api',
			timestamp: new Date().toISOString(),
			error: error instanceof Error ? error.message : 'Unknown error'
		})
	}
}

router.post('/place-order', placeOrder)
router.get('/chart-settings', getChartSettings)
router.get('/candles', getCandles)
router.get('/select-asset', selectAsset)
router.get('/balance', getBalance)
router.get('/status', getStatus)
router.get('/health', getHealth)

export default router
