{"name": "pocket-option-api", "module": "index.ts", "type": "module", "private": true, "scripts": {"start": "bun run index.ts"}, "devDependencies": {"@types/bun": "latest", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@types/uuid": "^10.0.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "picocolors": "^1.0.1", "socket.io-client": "^4.8.1", "technicalindicators": "^3.1.0", "uuid": "^11.1.0"}, "engines": {"bun": ">=1.0.0"}}