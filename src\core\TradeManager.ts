import { EventEmitter } from 'events'

export class TradeManager extends EventEmitter {
	private sessions: Map<string, TradeSession> = new Map()

	constructor(private options: TradeManagerOptions) {
		super()
		this.sessions.set(options.expiry, {
			steps: 0,
			lastTradeTime: 0,
			currentStake: options.baseStake,
			totalProfit: 0,
			totalLoss: 0,
			totalWins: 0,
			totalTrades: 0
		})
	}

	canTrade(expiry: string): boolean {
		const session = this.sessions.get(expiry)
		if (!session) return false

		const now = Date.now()
		const isCooldownOver = now - session.lastTradeTime >= this.options.cooldownMs
		const withinLossLimit = this.options.maxLoss === undefined || session.totalLoss < this.options.maxLoss
		const withinWinLimit = this.options.stopWin === undefined || session.totalProfit < this.options.stopWin

		if (!isCooldownOver) {
			const remaining = Math.ceil((this.options.cooldownMs - (now - session.lastTradeTime)) / 1000)
			this.emit('cooldown', { expiry, remaining })
			console.log(`[Cooldown] Waiting for ${expiry} - ${remaining}s remaining.`)
		}

		return isCooldownOver && withinLossLimit && withinWinLimit
	}

	getCurrentStake(expiry: string): number {
		const session = this.sessions.get(expiry)
		return session?.currentStake ?? this.options.baseStake
	}

	recordTradeStart(expiry: string): void {
		const session = this.sessions.get(expiry)
		if (session) {
			session.lastTradeTime = Date.now()
			this.emit('trade:start', { expiry, stake: session.currentStake })
		}
	}

	recordResult(expiry: string, result: 'win' | 'loss'): void {
		const session = this.sessions.get(expiry)
		if (!session) return

		session.totalTrades++
		if (result === 'win') {
			session.steps = 0
			session.currentStake = this.options.baseStake
			session.totalWins++
			session.totalProfit += session.currentStake
			this.emit('result', { expiry, result: 'win', stake: session.currentStake, profit: session.totalProfit })
			console.log(`[Trade Result] WIN | Profit: ${session.totalProfit.toFixed(2)}`)
		} else {
			session.steps++
			session.totalLoss += session.currentStake
			session.currentStake *= this.options.martingaleFactor
			this.emit('result', { expiry, result: 'loss', stake: session.currentStake, loss: session.totalLoss })
			console.log(`[Trade Result] LOSS | Accumulated Loss: ${session.totalLoss.toFixed(2)}`)
		}

		if (this.options.maxLoss && session.totalLoss >= this.options.maxLoss) {
			this.emit('limit', { type: 'maxLoss', value: session.totalLoss })
		}
		if (this.options.stopWin && session.totalProfit >= this.options.stopWin) {
			this.emit('limit', { type: 'stopWin', value: session.totalProfit })
		}

		this.emit('trade:end', {
			expiry,
			result,
			stake: session.currentStake,
			profit: session.totalProfit,
			loss: session.totalLoss,
			steps: session.steps,
			winRate: session.totalTrades > 0 ? session.totalWins / session.totalTrades : 0,
			totalTrades: session.totalTrades
		})
	}

	backtest(expiry: string, history: TradeOutcome[]): { winRate: number; totalProfit: number; trades: number } {
		const session = this.sessions.get(expiry)
		if (!session) throw new Error('Session not initialized')

		history.forEach(h => {
			this.recordTradeStart(expiry)
			this.recordResult(expiry, h.result)
		})

		return {
			winRate: session.totalWins / session.totalTrades,
			totalProfit: session.totalProfit - session.totalLoss,
			trades: session.totalTrades
		}
	}
}
