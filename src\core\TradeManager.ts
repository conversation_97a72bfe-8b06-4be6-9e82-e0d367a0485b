import { EventEmitter } from 'events'
import { v4 as uuidv4 } from 'uuid'
import { validateMoneyManagementConfig, type ValidationResult } from '../utils/moneyManagementConfig'

export class TradeManager extends EventEmitter {
	private sessions: Map<string, TradeSession> = new Map()
	private enhancedSessions: Map<string, TradingSession> = new Map()
	private moneyManagement: MoneyManagementConfig
	private currentBalance: number = 0

	constructor(private options: TradeManagerOptions) {
		super()

		// Initialize money management configuration with defaults
		this.moneyManagement = this.initializeMoneyManagement(options.moneyManagement)

		// Legacy session for backward compatibility
		this.sessions.set(options.expiry, {
			steps: 0,
			lastTradeTime: 0,
			currentStake: options.baseStake,
			totalProfit: 0,
			totalLoss: 0,
			totalWins: 0,
			totalTrades: 0
		})

		// Enhanced session for new features
		if (options.enableDetailedTracking) {
			this.initializeEnhancedSession(options.expiry)
		}
	}

	/**
	 * Initialize money management configuration with defaults
	 */
	private initializeMoneyManagement(config?: MoneyManagementConfig): MoneyManagementConfig {
		return {
			baseStake: config?.baseStake ?? this.options.baseStake,
			maxStakePercent: config?.maxStakePercent ?? 5, // 5% of balance max
			martingaleStrategy: config?.martingaleStrategy ?? 'fixed',
			martingaleFactor: config?.martingaleFactor ?? this.options.martingaleFactor,
			maxMartingaleSteps: config?.maxMartingaleSteps ?? this.options.maxSteps,
			martingaleResetOnWin: config?.martingaleResetOnWin ?? true,
			targetProfit: config?.targetProfit ?? this.options.stopWin,
			maxLoss: config?.maxLoss ?? this.options.maxLoss,
			maxTrades: config?.maxTrades,
			minCapitalRequired: config?.minCapitalRequired ?? this.options.baseStake * 10,
			maxRiskPerTrade: config?.maxRiskPerTrade ?? this.options.baseStake * 5,
			capitalPreservationMode: config?.capitalPreservationMode ?? false,
			emergencyStopLoss: config?.emergencyStopLoss ?? this.options.baseStake * 20
		}
	}

	/**
	 * Initialize enhanced trading session
	 */
	private initializeEnhancedSession(expiry: string): void {
		const sessionId = this.options.sessionId ?? uuidv4()
		const now = Date.now()

		const enhancedSession: TradingSession = {
			id: sessionId,
			startTime: now,
			state: 'INITIALIZING',
			steps: 0,
			lastTradeTime: 0,
			currentStake: this.moneyManagement.baseStake,
			initialBalance: 0, // Will be set when balance is fetched
			currentBalance: 0,
			minBalance: 0,
			maxBalance: 0,
			totalProfit: 0,
			totalLoss: 0,
			totalWins: 0,
			totalTrades: 0,
			winStreak: 0,
			lossStreak: 0,
			maxWinStreak: 0,
			maxLossStreak: 0,
			trades: [],
			largestWin: 0,
			largestLoss: 0,
			winRate: 0,
			profitFactor: 0,
			averageWin: 0,
			averageLoss: 0,
			expectancy: 0
		}

		this.enhancedSessions.set(expiry, enhancedSession)
	}

	/**
	 * Calculate next stake using enhanced martingale system
	 */
	private calculateNextStake(session: TradingSession, isWin: boolean): number {
		if (isWin && this.moneyManagement.martingaleResetOnWin) {
			return this.moneyManagement.baseStake
		}

		if (isWin) {
			return session.currentStake // Keep same stake on win if not resetting
		}

		// Calculate next stake based on martingale strategy
		let nextStake: number

		switch (this.moneyManagement.martingaleStrategy) {
			case 'fixed':
				nextStake = session.currentStake * this.moneyManagement.martingaleFactor
				break

			case 'progressive':
				// Progressive martingale: increase factor with each step
				const progressiveFactor = this.moneyManagement.martingaleFactor + session.steps * 0.1
				nextStake = session.currentStake * progressiveFactor
				break

			case 'fibonacci':
				nextStake = this.calculateFibonacciStake(session.steps)
				break

			case 'custom':
				// Custom logic can be implemented here
				nextStake = this.calculateCustomMartingale(session)
				break

			default:
				nextStake = session.currentStake * this.moneyManagement.martingaleFactor
		}

		// Apply safety limits
		return this.applySafetyLimits(nextStake)
	}

	/**
	 * Calculate Fibonacci-based stake
	 */
	private calculateFibonacciStake(step: number): number {
		const fibSequence = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144]
		const fibIndex = Math.min(step, fibSequence.length - 1)
		const fibValue = fibSequence[fibIndex] ?? 1
		return this.moneyManagement.baseStake * fibValue
	}

	/**
	 * Calculate custom martingale strategy
	 */
	private calculateCustomMartingale(session: TradingSession): number {
		// Example custom logic: reduce multiplier as losses increase
		const reducedFactor = Math.max(1.1, this.moneyManagement.martingaleFactor - session.steps * 0.1)
		return session.currentStake * reducedFactor
	}

	/**
	 * Apply safety limits to calculated stake
	 */
	private applySafetyLimits(calculatedStake: number): number {
		// Don't exceed maximum risk per trade
		let safeStake = Math.min(calculatedStake, this.moneyManagement.maxRiskPerTrade)

		// Don't exceed percentage of current balance
		if (this.currentBalance > 0) {
			const maxByPercent = this.currentBalance * (this.moneyManagement.maxStakePercent / 100)
			safeStake = Math.min(safeStake, maxByPercent)
		}

		// Ensure minimum stake
		safeStake = Math.max(safeStake, this.moneyManagement.baseStake)

		// Capital preservation mode
		if (this.moneyManagement.capitalPreservationMode && this.currentBalance > 0) {
			const conservativeMax = this.currentBalance * 0.02 // 2% max in preservation mode
			safeStake = Math.min(safeStake, conservativeMax)
		}

		return Math.round(safeStake * 100) / 100 // Round to 2 decimal places
	}

	/**
	 * Update current balance
	 */
	async updateBalance(): Promise<void> {
		if (this.options.getBalance) {
			try {
				this.currentBalance = await this.options.getBalance()

				// Update enhanced sessions with current balance
				for (const session of this.enhancedSessions.values()) {
					if (session.initialBalance === 0) {
						session.initialBalance = this.currentBalance
						session.currentBalance = this.currentBalance
						session.minBalance = this.currentBalance
						session.maxBalance = this.currentBalance
						session.state = 'ACTIVE'
					} else {
						session.currentBalance = this.currentBalance
						session.minBalance = Math.min(session.minBalance, this.currentBalance)
						session.maxBalance = Math.max(session.maxBalance, this.currentBalance)
					}
				}
			} catch (error) {
				console.error('[TradeManager] Failed to update balance:', error)
			}
		}
	}

	async canTrade(expiry: string): Promise<boolean> {
		const session = this.sessions.get(expiry)
		const enhancedSession = this.enhancedSessions.get(expiry)

		if (!session) return false

		// Update balance before checking trading conditions
		await this.updateBalance()

		const now = Date.now()
		const isCooldownOver = now - session.lastTradeTime >= this.options.cooldownMs

		// Basic checks
		if (!isCooldownOver) {
			const remaining = Math.ceil((this.options.cooldownMs - (now - session.lastTradeTime)) / 1000)
			this.emit('cooldown', { expiry, remaining })
			console.log(`[Cooldown] Waiting for ${expiry} - ${remaining}s remaining.`)
			return false
		}

		// Enhanced money management checks
		if (enhancedSession) {
			return this.canTradeEnhanced(enhancedSession)
		}

		// Legacy checks for backward compatibility
		const withinLossLimit = this.options.maxLoss === undefined || session.totalLoss < this.options.maxLoss
		const withinWinLimit = this.options.stopWin === undefined || session.totalProfit < this.options.stopWin

		return withinLossLimit && withinWinLimit
	}

	/**
	 * Enhanced trading permission checks
	 */
	private canTradeEnhanced(session: TradingSession): boolean {
		// Check session state
		if (session.state !== 'ACTIVE') {
			console.log(`[TradeManager] Trading disabled - Session state: ${session.state}`)
			return false
		}

		// Check target profit
		if (this.moneyManagement.targetProfit && session.totalProfit >= this.moneyManagement.targetProfit) {
			this.terminateSession(session, 'target_profit_reached')
			return false
		}

		// Check maximum loss
		if (this.moneyManagement.maxLoss && session.totalLoss >= this.moneyManagement.maxLoss) {
			this.terminateSession(session, 'max_loss_reached')
			return false
		}

		// Check maximum trades
		if (this.moneyManagement.maxTrades && session.totalTrades >= this.moneyManagement.maxTrades) {
			this.terminateSession(session, 'max_trades_reached')
			return false
		}

		// Check minimum capital requirement
		if (this.currentBalance < this.moneyManagement.minCapitalRequired) {
			this.terminateSession(session, 'capital_depleted')
			return false
		}

		// Check emergency stop loss
		if (session.totalLoss >= this.moneyManagement.emergencyStopLoss) {
			this.terminateSession(session, 'max_loss_reached')
			return false
		}

		// Check maximum martingale steps
		if (session.steps >= this.moneyManagement.maxMartingaleSteps) {
			console.log(`[TradeManager] Maximum martingale steps reached: ${session.steps}`)
			return false
		}

		// Check if we have sufficient balance for next trade
		const nextStake = this.calculateNextStake(session, false) // Assume loss for safety
		if (this.currentBalance < nextStake) {
			this.terminateSession(session, 'insufficient_balance')
			return false
		}

		return true
	}

	/**
	 * Terminate trading session
	 */
	private terminateSession(session: TradingSession, reason: SessionTerminationReason): void {
		session.state = 'TERMINATED'
		session.terminationReason = reason
		session.endTime = Date.now()

		this.emit('session:terminated', {
			sessionId: session.id,
			reason,
			finalBalance: this.currentBalance,
			totalProfit: session.totalProfit,
			totalLoss: session.totalLoss,
			totalTrades: session.totalTrades
		})

		console.log(`[TradeManager] Session terminated: ${reason}`)
	}

	getCurrentStake(expiry: string): number {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (enhancedSession) {
			return enhancedSession.currentStake
		}

		const session = this.sessions.get(expiry)
		return session?.currentStake ?? this.options.baseStake
	}

	/**
	 * Get next stake amount for planning purposes
	 */
	getNextStake(expiry: string, assumeResult: 'win' | 'loss'): number {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (enhancedSession) {
			return this.calculateNextStake(enhancedSession, assumeResult === 'win')
		}

		const session = this.sessions.get(expiry)
		if (!session) return this.options.baseStake

		if (assumeResult === 'win') {
			return this.options.baseStake
		} else {
			return session.currentStake * this.options.martingaleFactor
		}
	}

	async recordTradeStart(
		expiry: string,
		signal?: Signal,
		confidence?: 'low' | 'medium' | 'high',
		reason?: string
	): Promise<void> {
		const session = this.sessions.get(expiry)
		const enhancedSession = this.enhancedSessions.get(expiry)

		if (session) {
			session.lastTradeTime = Date.now()
		}

		if (enhancedSession) {
			// Update balance before trade
			await this.updateBalance()

			// Create trade record
			const tradeRecord: TradeRecord = {
				id: uuidv4(),
				timestamp: Date.now(),
				signal: signal ?? 'BUY',
				stake: enhancedSession.currentStake,
				result: 'pending',
				profit: 0,
				loss: 0,
				martingaleStep: enhancedSession.steps,
				balanceBefore: this.currentBalance,
				balanceAfter: this.currentBalance,
				reason: reason ?? 'Trade started',
				confidence: confidence ?? 'medium'
			}

			enhancedSession.trades.push(tradeRecord)
			enhancedSession.lastTradeTime = Date.now()

			this.emit('trade:start', {
				expiry,
				stake: enhancedSession.currentStake,
				tradeId: tradeRecord.id,
				signal,
				confidence,
				balance: this.currentBalance
			})
		} else {
			this.emit('trade:start', { expiry, stake: session?.currentStake ?? this.options.baseStake })
		}
	}

	async recordResult(expiry: string, result: 'win' | 'loss', tradeAmount?: number): Promise<void> {
		const session = this.sessions.get(expiry)
		const enhancedSession = this.enhancedSessions.get(expiry)

		if (!session) return

		// Update balance after trade
		await this.updateBalance()

		// Legacy session update
		session.totalTrades++
		if (result === 'win') {
			session.steps = 0
			session.currentStake = this.options.baseStake
			session.totalWins++
			session.totalProfit += session.currentStake
		} else {
			session.steps++
			session.totalLoss += session.currentStake
			session.currentStake *= this.options.martingaleFactor
		}

		// Enhanced session update
		if (enhancedSession) {
			this.updateEnhancedSessionResult(enhancedSession, result, tradeAmount)
		}

		// Legacy limit checks
		if (this.options.maxLoss && session.totalLoss >= this.options.maxLoss) {
			this.emit('limit', { type: 'maxLoss', value: session.totalLoss })
		}
		if (this.options.stopWin && session.totalProfit >= this.options.stopWin) {
			this.emit('limit', { type: 'stopWin', value: session.totalProfit })
		}

		this.emit('trade:end', {
			expiry,
			result,
			stake: session.currentStake,
			profit: session.totalProfit,
			loss: session.totalLoss,
			steps: session.steps,
			winRate: session.totalTrades > 0 ? session.totalWins / session.totalTrades : 0,
			totalTrades: session.totalTrades,
			balance: this.currentBalance
		})

		console.log(
			`[Trade Result] ${result.toUpperCase()} | Balance: ${this.currentBalance.toFixed(2)} | ${
				result === 'win' ? 'Profit' : 'Loss'
			}: ${(result === 'win' ? session.totalProfit : session.totalLoss).toFixed(2)}`
		)
	}

	/**
	 * Update enhanced session with trade result
	 */
	private updateEnhancedSessionResult(session: TradingSession, result: 'win' | 'loss', tradeAmount?: number): void {
		// Find the most recent pending trade
		const pendingTrade = session.trades.findLast(trade => trade.result === 'pending')

		if (pendingTrade) {
			pendingTrade.result = result
			pendingTrade.balanceAfter = this.currentBalance

			if (result === 'win') {
				const profit = tradeAmount ?? pendingTrade.stake * 0.8 // Assume 80% payout if not specified
				pendingTrade.profit = profit
				session.totalProfit += profit
				session.totalWins++

				// Update win streak
				session.lossStreak = 0
				session.winStreak++
				session.maxWinStreak = Math.max(session.maxWinStreak, session.winStreak)

				// Update largest win
				session.largestWin = Math.max(session.largestWin, profit)

				// Reset martingale if configured
				if (this.moneyManagement.martingaleResetOnWin) {
					session.steps = 0
					session.currentStake = this.moneyManagement.baseStake
				}
			} else {
				const loss = pendingTrade.stake
				pendingTrade.loss = loss
				session.totalLoss += loss

				// Update loss streak
				session.winStreak = 0
				session.lossStreak++
				session.maxLossStreak = Math.max(session.maxLossStreak, session.lossStreak)

				// Update largest loss
				session.largestLoss = Math.max(session.largestLoss, loss)

				// Calculate next stake using enhanced martingale
				session.steps++
				session.currentStake = this.calculateNextStake(session, false)
			}
		}

		// Update session statistics
		session.totalTrades++
		session.winRate = session.totalTrades > 0 ? session.totalWins / session.totalTrades : 0

		// Calculate performance metrics
		this.updatePerformanceMetrics(session)
	}

	/**
	 * Update performance metrics for enhanced session
	 */
	private updatePerformanceMetrics(session: TradingSession): void {
		if (session.totalTrades === 0) return

		// Calculate averages
		session.averageWin = session.totalWins > 0 ? session.totalProfit / session.totalWins : 0
		session.averageLoss =
			session.totalTrades - session.totalWins > 0 ? session.totalLoss / (session.totalTrades - session.totalWins) : 0

		// Calculate profit factor
		session.profitFactor =
			session.totalLoss > 0 ? session.totalProfit / session.totalLoss : session.totalProfit > 0 ? Infinity : 0

		// Calculate expectancy
		session.expectancy = session.winRate * session.averageWin - (1 - session.winRate) * session.averageLoss
	}

	/**
	 * Get current balance
	 */
	getCurrentBalance(): number {
		return this.currentBalance
	}

	/**
	 * Check if sufficient capital is available for trading
	 */
	hasSufficientCapital(expiry: string): boolean {
		const nextStake = this.getNextStake(expiry, 'loss') // Check worst case
		return this.currentBalance >= nextStake && this.currentBalance >= this.moneyManagement.minCapitalRequired
	}

	/**
	 * Get capital utilization percentage
	 */
	getCapitalUtilization(): number {
		if (this.currentBalance === 0) return 0
		const enhancedSession = Array.from(this.enhancedSessions.values())[0]
		if (!enhancedSession) return 0

		const totalRisk = enhancedSession.currentStake
		return (totalRisk / this.currentBalance) * 100
	}

	/**
	 * Check if capital preservation mode should be activated
	 */
	shouldActivateCapitalPreservation(): boolean {
		if (this.currentBalance === 0) return true

		const enhancedSession = Array.from(this.enhancedSessions.values())[0]
		if (!enhancedSession) return false

		// Activate if balance dropped below 50% of initial
		const balanceDropPercent =
			((enhancedSession.initialBalance - this.currentBalance) / enhancedSession.initialBalance) * 100
		return balanceDropPercent > 50
	}

	/**
	 * Get risk assessment for current session
	 */
	getRiskAssessment(expiry: string): {
		riskLevel: 'low' | 'medium' | 'high' | 'critical'
		capitalUtilization: number
		nextStakeRisk: number
		recommendedAction: string
	} {
		const capitalUtilization = this.getCapitalUtilization()
		const nextStake = this.getNextStake(expiry, 'loss')
		const nextStakeRisk = this.currentBalance > 0 ? (nextStake / this.currentBalance) * 100 : 100

		let riskLevel: 'low' | 'medium' | 'high' | 'critical'
		let recommendedAction: string

		if (capitalUtilization < 5 && nextStakeRisk < 10) {
			riskLevel = 'low'
			recommendedAction = 'Continue trading with current parameters'
		} else if (capitalUtilization < 15 && nextStakeRisk < 25) {
			riskLevel = 'medium'
			recommendedAction = 'Monitor closely, consider reducing stake if losses continue'
		} else if (capitalUtilization < 30 && nextStakeRisk < 50) {
			riskLevel = 'high'
			recommendedAction = 'High risk detected, consider stopping or reducing position size'
		} else {
			riskLevel = 'critical'
			recommendedAction = 'Critical risk level, stop trading immediately'
		}

		return {
			riskLevel,
			capitalUtilization,
			nextStakeRisk,
			recommendedAction
		}
	}

	backtest(expiry: string, history: TradeOutcome[]): { winRate: number; totalProfit: number; trades: number } {
		const session = this.sessions.get(expiry)
		if (!session) throw new Error('Session not initialized')

		history.forEach(h => {
			this.recordTradeStart(expiry)
			this.recordResult(expiry, h.result)
		})

		return {
			winRate: session.totalWins / session.totalTrades,
			totalProfit: session.totalProfit - session.totalLoss,
			trades: session.totalTrades
		}
	}

	/**
	 * Pause trading session
	 */
	pauseSession(expiry: string, reason?: string): void {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (enhancedSession && enhancedSession.state === 'ACTIVE') {
			enhancedSession.state = 'PAUSED'

			this.emit('session:paused', {
				sessionId: enhancedSession.id,
				reason: reason ?? 'Manual pause',
				timestamp: Date.now()
			})

			console.log(`[TradeManager] Session paused: ${reason ?? 'Manual pause'}`)
		}
	}

	/**
	 * Resume trading session
	 */
	resumeSession(expiry: string): void {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (enhancedSession && enhancedSession.state === 'PAUSED') {
			enhancedSession.state = 'ACTIVE'

			this.emit('session:resumed', {
				sessionId: enhancedSession.id,
				timestamp: Date.now()
			})

			console.log(`[TradeManager] Session resumed`)
		}
	}

	/**
	 * Manually stop trading session
	 */
	stopSession(expiry: string, reason?: string): void {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (enhancedSession) {
			this.terminateSession(enhancedSession, 'manual_stop')
			console.log(`[TradeManager] Session manually stopped: ${reason ?? 'User request'}`)
		}
	}

	/**
	 * Get current session state
	 */
	getSessionState(expiry: string): TradingSessionState | null {
		const enhancedSession = this.enhancedSessions.get(expiry)
		return enhancedSession?.state ?? null
	}

	/**
	 * Check if session should be terminated based on current conditions
	 */
	checkSessionTerminationConditions(expiry: string): boolean {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (!enhancedSession || enhancedSession.state !== 'ACTIVE') {
			return false
		}

		// Check target profit
		if (this.moneyManagement.targetProfit && enhancedSession.totalProfit >= this.moneyManagement.targetProfit) {
			this.terminateSession(enhancedSession, 'target_profit_reached')
			return true
		}

		// Check maximum loss
		if (this.moneyManagement.maxLoss && enhancedSession.totalLoss >= this.moneyManagement.maxLoss) {
			this.terminateSession(enhancedSession, 'max_loss_reached')
			return true
		}

		// Check maximum trades
		if (this.moneyManagement.maxTrades && enhancedSession.totalTrades >= this.moneyManagement.maxTrades) {
			this.terminateSession(enhancedSession, 'max_trades_reached')
			return true
		}

		// Check capital depletion
		if (this.currentBalance < this.moneyManagement.minCapitalRequired) {
			this.terminateSession(enhancedSession, 'capital_depleted')
			return true
		}

		// Check emergency stop loss
		if (enhancedSession.totalLoss >= this.moneyManagement.emergencyStopLoss) {
			this.terminateSession(enhancedSession, 'max_loss_reached')
			return true
		}

		return false
	}

	/**
	 * Get session duration in milliseconds
	 */
	getSessionDuration(expiry: string): number {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (!enhancedSession) return 0

		const endTime = enhancedSession.endTime ?? Date.now()
		return endTime - enhancedSession.startTime
	}

	/**
	 * Get session progress summary
	 */
	getSessionProgress(expiry: string): {
		duration: number
		tradesCompleted: number
		currentProfit: number
		currentLoss: number
		winRate: number
		targetProgress: number
		riskLevel: string
	} | null {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (!enhancedSession) return null

		const duration = this.getSessionDuration(expiry)
		const targetProgress = this.moneyManagement.targetProfit
			? (enhancedSession.totalProfit / this.moneyManagement.targetProfit) * 100
			: 0

		const riskAssessment = this.getRiskAssessment(expiry)

		return {
			duration,
			tradesCompleted: enhancedSession.totalTrades,
			currentProfit: enhancedSession.totalProfit,
			currentLoss: enhancedSession.totalLoss,
			winRate: enhancedSession.winRate * 100,
			targetProgress: Math.min(targetProgress, 100),
			riskLevel: riskAssessment.riskLevel
		}
	}

	/**
	 * Enable/disable capital preservation mode
	 */
	setCapitalPreservationMode(enabled: boolean): void {
		this.moneyManagement.capitalPreservationMode = enabled

		this.emit('capital_preservation_mode', {
			enabled,
			timestamp: Date.now()
		})

		console.log(`[TradeManager] Capital preservation mode ${enabled ? 'enabled' : 'disabled'}`)
	}

	/**
	 * Update money management configuration with validation
	 */
	updateMoneyManagement(updates: Partial<MoneyManagementConfig>): ValidationResult {
		const newConfig = { ...this.moneyManagement, ...updates }
		const validation = validateMoneyManagementConfig(newConfig)

		if (!validation.isValid) {
			this.emit('money_management_validation_failed', {
				updates,
				validation,
				timestamp: Date.now()
			})

			console.error(`[TradeManager] Money management validation failed:`, validation.errors)
			return validation
		}

		// Apply warnings if any
		if (validation.warnings.length > 0) {
			console.warn(`[TradeManager] Money management warnings:`, validation.warnings)
		}

		this.moneyManagement = newConfig as MoneyManagementConfig

		this.emit('money_management_updated', {
			updates,
			validation,
			timestamp: Date.now()
		})

		console.log(`[TradeManager] Money management configuration updated successfully`)
		return validation
	}

	/**
	 * Validate current money management configuration
	 */
	validateConfiguration(): ValidationResult {
		return validateMoneyManagementConfig(this.moneyManagement)
	}

	/**
	 * Get configuration validation status
	 */
	getConfigurationStatus(): {
		isValid: boolean
		hasWarnings: boolean
		errorCount: number
		warningCount: number
		lastValidated: number
	} {
		const validation = this.validateConfiguration()

		return {
			isValid: validation.isValid,
			hasWarnings: validation.warnings.length > 0,
			errorCount: validation.errors.length,
			warningCount: validation.warnings.length,
			lastValidated: Date.now()
		}
	}

	/**
	 * Apply configuration with validation
	 */
	applyConfiguration(config: MoneyManagementConfig): ValidationResult {
		const validation = validateMoneyManagementConfig(config)

		if (validation.isValid) {
			this.moneyManagement = { ...config }

			this.emit('configuration_applied', {
				config,
				validation,
				timestamp: Date.now()
			})

			console.log(`[TradeManager] New configuration applied successfully`)
		} else {
			console.error(`[TradeManager] Configuration application failed:`, validation.errors)
		}

		return validation
	}

	/**
	 * Get enhanced session for external access
	 */
	getEnhancedSession(expiry: string): TradingSession | null {
		return this.enhancedSessions.get(expiry) ?? null
	}

	/**
	 * Get money management configuration
	 */
	getMoneyManagementConfig(): MoneyManagementConfig {
		return { ...this.moneyManagement }
	}

	/**
	 * Get all trade records for a session
	 */
	getTradeRecords(expiry: string): TradeRecord[] {
		const enhancedSession = this.enhancedSessions.get(expiry)
		return enhancedSession?.trades ?? []
	}

	/**
	 * Get trade records filtered by result
	 */
	getTradesByResult(expiry: string, result: 'win' | 'loss' | 'pending'): TradeRecord[] {
		const trades = this.getTradeRecords(expiry)
		return trades.filter(trade => trade.result === result)
	}

	/**
	 * Get trade records within a time range
	 */
	getTradesInTimeRange(expiry: string, startTime: number, endTime: number): TradeRecord[] {
		const trades = this.getTradeRecords(expiry)
		return trades.filter(trade => trade.timestamp >= startTime && trade.timestamp <= endTime)
	}

	/**
	 * Get trade statistics for analysis
	 */
	getTradeStatistics(expiry: string): {
		totalTrades: number
		winningTrades: number
		losingTrades: number
		pendingTrades: number
		averageTradeTime: number
		averageWinAmount: number
		averageLossAmount: number
		largestWin: number
		largestLoss: number
		currentStreak: { type: 'win' | 'loss'; count: number }
		longestWinStreak: number
		longestLossStreak: number
		profitFactor: number
		winRate: number
		expectancy: number
	} | null {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (!enhancedSession) return null

		const trades = enhancedSession.trades
		const winningTrades = trades.filter(t => t.result === 'win')
		const losingTrades = trades.filter(t => t.result === 'loss')
		const pendingTrades = trades.filter(t => t.result === 'pending')

		// Calculate average trade time (time between trades)
		let averageTradeTime = 0
		if (trades.length > 1) {
			const timeDiffs = []
			for (let i = 1; i < trades.length; i++) {
				const currentTrade = trades[i]
				const previousTrade = trades[i - 1]
				if (currentTrade && previousTrade) {
					timeDiffs.push(currentTrade.timestamp - previousTrade.timestamp)
				}
			}
			averageTradeTime = timeDiffs.length > 0 ? timeDiffs.reduce((sum, diff) => sum + diff, 0) / timeDiffs.length : 0
		}

		// Calculate averages
		const averageWinAmount =
			winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.profit, 0) / winningTrades.length : 0
		const averageLossAmount =
			losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.loss, 0) / losingTrades.length : 0

		// Determine current streak
		let currentStreak = { type: 'win' as 'win' | 'loss', count: 0 }
		if (trades.length > 0) {
			const lastTrade = trades[trades.length - 1]
			if (lastTrade && lastTrade.result !== 'pending') {
				currentStreak.type = lastTrade.result
				currentStreak.count = lastTrade.result === 'win' ? enhancedSession.winStreak : enhancedSession.lossStreak
			}
		}

		return {
			totalTrades: trades.length,
			winningTrades: winningTrades.length,
			losingTrades: losingTrades.length,
			pendingTrades: pendingTrades.length,
			averageTradeTime,
			averageWinAmount,
			averageLossAmount,
			largestWin: enhancedSession.largestWin,
			largestLoss: enhancedSession.largestLoss,
			currentStreak,
			longestWinStreak: enhancedSession.maxWinStreak,
			longestLossStreak: enhancedSession.maxLossStreak,
			profitFactor: enhancedSession.profitFactor,
			winRate: enhancedSession.winRate,
			expectancy: enhancedSession.expectancy
		}
	}

	/**
	 * Export trade records to CSV format
	 */
	exportTradesToCSV(expiry: string): string {
		const trades = this.getTradeRecords(expiry)
		if (trades.length === 0) return ''

		const headers = [
			'ID',
			'Timestamp',
			'Date',
			'Signal',
			'Stake',
			'Result',
			'Profit',
			'Loss',
			'Martingale Step',
			'Balance Before',
			'Balance After',
			'Reason',
			'Confidence'
		]

		const csvRows = [headers.join(',')]

		trades.forEach(trade => {
			const row = [
				trade.id,
				trade.timestamp.toString(),
				new Date(trade.timestamp).toISOString(),
				trade.signal,
				trade.stake.toString(),
				trade.result,
				trade.profit.toString(),
				trade.loss.toString(),
				trade.martingaleStep.toString(),
				trade.balanceBefore.toString(),
				trade.balanceAfter.toString(),
				`"${trade.reason}"`, // Quoted to handle commas in reason
				trade.confidence
			]
			csvRows.push(row.join(','))
		})

		return csvRows.join('\n')
	}

	/**
	 * Get performance metrics over time
	 */
	getPerformanceOverTime(
		expiry: string,
		intervalMs: number = 3600000
	): {
		// Default 1 hour intervals
		timestamp: number
		cumulativeProfit: number
		cumulativeLoss: number
		netProfit: number
		tradesCount: number
		winRate: number
	}[] {
		const trades = this.getTradeRecords(expiry).filter(t => t.result !== 'pending')
		if (trades.length === 0) return []

		const intervals: Map<
			number,
			{
				profit: number
				loss: number
				trades: number
				wins: number
			}
		> = new Map()

		// Group trades by time intervals
		trades.forEach(trade => {
			const intervalStart = Math.floor(trade.timestamp / intervalMs) * intervalMs
			const existing = intervals.get(intervalStart) || { profit: 0, loss: 0, trades: 0, wins: 0 }

			existing.profit += trade.profit
			existing.loss += trade.loss
			existing.trades++
			if (trade.result === 'win') existing.wins++

			intervals.set(intervalStart, existing)
		})

		// Convert to cumulative performance data
		const sortedIntervals = Array.from(intervals.entries()).sort(([a], [b]) => a - b)
		let cumulativeProfit = 0
		let cumulativeLoss = 0

		return sortedIntervals.map(([timestamp, data]) => {
			cumulativeProfit += data.profit
			cumulativeLoss += data.loss

			return {
				timestamp,
				cumulativeProfit,
				cumulativeLoss,
				netProfit: cumulativeProfit - cumulativeLoss,
				tradesCount: data.trades,
				winRate: data.trades > 0 ? data.wins / data.trades : 0
			}
		})
	}

	/**
	 * Generate comprehensive session summary
	 */
	generateSessionSummary(expiry: string): SessionSummary | null {
		const enhancedSession = this.enhancedSessions.get(expiry)
		if (!enhancedSession) return null

		const duration = this.getSessionDuration(expiry)
		const trades = enhancedSession.trades.filter(t => t.result !== 'pending')

		// Calculate performance metrics
		const totalReturn = enhancedSession.totalProfit - enhancedSession.totalLoss
		const totalReturnPercent =
			enhancedSession.initialBalance > 0 ? (totalReturn / enhancedSession.initialBalance) * 100 : 0

		// Calculate drawdown
		const maxDrawdown = enhancedSession.initialBalance - enhancedSession.minBalance
		const maxDrawdownPercent =
			enhancedSession.initialBalance > 0 ? (maxDrawdown / enhancedSession.initialBalance) * 100 : 0

		// Calculate Sharpe ratio (simplified version)
		const averageReturn = trades.length > 0 ? totalReturn / trades.length : 0
		const returns = trades.map(t => (t.result === 'win' ? t.profit : -t.loss))
		const returnStdDev = this.calculateStandardDeviation(returns)
		const sharpeRatio = returnStdDev > 0 ? averageReturn / returnStdDev : 0

		// Calculate average trade return
		const averageTradeReturn = trades.length > 0 ? totalReturn / trades.length : 0

		// Calculate trades per hour
		const durationHours = duration / (1000 * 60 * 60)
		const tradesPerHour = durationHours > 0 ? trades.length / durationHours : 0

		// Calculate average trade time
		let averageTradeTime = 0
		if (trades.length > 1) {
			const firstTrade = trades[0]
			const lastTrade = trades[trades.length - 1]
			if (firstTrade && lastTrade) {
				averageTradeTime = (lastTrade.timestamp - firstTrade.timestamp) / (trades.length - 1)
			}
		}

		const sessionSummary: SessionSummary = {
			session: enhancedSession,
			performance: {
				totalReturn,
				totalReturnPercent,
				winRate: enhancedSession.winRate * 100,
				profitFactor: enhancedSession.profitFactor,
				sharpeRatio,
				maxDrawdown,
				maxDrawdownPercent,
				averageTradeReturn,
				averageWin: enhancedSession.averageWin,
				averageLoss: enhancedSession.averageLoss,
				expectancy: enhancedSession.expectancy,
				largestWin: enhancedSession.largestWin,
				largestLoss: enhancedSession.largestLoss,
				consecutiveWins: enhancedSession.maxWinStreak,
				consecutiveLosses: enhancedSession.maxLossStreak
			},
			statistics: {
				totalTrades: enhancedSession.totalTrades,
				winningTrades: enhancedSession.totalWins,
				losingTrades: enhancedSession.totalTrades - enhancedSession.totalWins,
				duration,
				tradesPerHour,
				averageTradeTime
			},
			termination: {
				reason: enhancedSession.terminationReason ?? 'session_active',
				timestamp: enhancedSession.endTime ?? Date.now(),
				finalBalance: this.currentBalance,
				message: this.getTerminationMessage(enhancedSession.terminationReason)
			}
		}

		return sessionSummary
	}

	/**
	 * Calculate standard deviation of an array of numbers
	 */
	private calculateStandardDeviation(values: number[]): number {
		if (values.length === 0) return 0

		const mean = values.reduce((sum, val) => sum + val, 0) / values.length
		const squaredDiffs = values.map(val => Math.pow(val - mean, 2))
		const variance = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length

		return Math.sqrt(variance)
	}

	/**
	 * Get human-readable termination message
	 */
	private getTerminationMessage(reason?: SessionTerminationReason): string {
		switch (reason) {
			case 'target_profit_reached':
				return 'Session ended successfully - target profit achieved'
			case 'max_loss_reached':
				return 'Session terminated - maximum loss limit reached'
			case 'capital_depleted':
				return 'Session terminated - insufficient capital to continue'
			case 'max_trades_reached':
				return 'Session completed - maximum number of trades reached'
			case 'manual_stop':
				return 'Session stopped manually by user'
			case 'broker_error':
				return 'Session terminated due to broker connection error'
			case 'insufficient_balance':
				return 'Session terminated - insufficient balance for next trade'
			default:
				return 'Session is currently active'
		}
	}

	/**
	 * Display formatted session summary to console
	 */
	displaySessionSummary(expiry: string): void {
		const summary = this.generateSessionSummary(expiry)
		if (!summary) {
			console.log('[TradeManager] No session summary available')
			return
		}

		const { session, performance, statistics, termination } = summary
		const durationHours = statistics.duration / (1000 * 60 * 60)
		const durationMinutes = (statistics.duration % (1000 * 60 * 60)) / (1000 * 60)

		console.log('\n' + '='.repeat(60))
		console.log('                    TRADING SESSION SUMMARY')
		console.log('='.repeat(60))

		// Session Info
		console.log(`Session ID: ${session.id}`)
		console.log(`Start Time: ${new Date(session.startTime).toLocaleString()}`)
		console.log(`End Time: ${session.endTime ? new Date(session.endTime).toLocaleString() : 'Active'}`)
		console.log(`Duration: ${Math.floor(durationHours)}h ${Math.floor(durationMinutes)}m`)
		console.log(`Status: ${session.state}`)

		console.log('\n' + '-'.repeat(30) + ' PERFORMANCE ' + '-'.repeat(30))

		// Performance Metrics
		console.log(`Initial Balance: $${session.initialBalance.toFixed(2)}`)
		console.log(`Final Balance: $${termination.finalBalance.toFixed(2)}`)
		console.log(`Total Return: $${performance.totalReturn.toFixed(2)} (${performance.totalReturnPercent.toFixed(2)}%)`)
		console.log(`Total Profit: $${session.totalProfit.toFixed(2)}`)
		console.log(`Total Loss: $${session.totalLoss.toFixed(2)}`)
		console.log(`Max Drawdown: $${performance.maxDrawdown.toFixed(2)} (${performance.maxDrawdownPercent.toFixed(2)}%)`)

		console.log('\n' + '-'.repeat(30) + ' STATISTICS ' + '-'.repeat(30))

		// Trade Statistics
		console.log(`Total Trades: ${statistics.totalTrades}`)
		console.log(`Winning Trades: ${statistics.winningTrades}`)
		console.log(`Losing Trades: ${statistics.losingTrades}`)
		console.log(`Win Rate: ${performance.winRate.toFixed(2)}%`)
		console.log(`Profit Factor: ${performance.profitFactor.toFixed(2)}`)
		console.log(`Expectancy: $${performance.expectancy.toFixed(2)}`)

		console.log('\n' + '-'.repeat(30) + ' TRADE DETAILS ' + '-'.repeat(30))

		// Trade Details
		console.log(`Average Win: $${performance.averageWin.toFixed(2)}`)
		console.log(`Average Loss: $${performance.averageLoss.toFixed(2)}`)
		console.log(`Largest Win: $${performance.largestWin.toFixed(2)}`)
		console.log(`Largest Loss: $${performance.largestLoss.toFixed(2)}`)
		console.log(`Longest Win Streak: ${performance.consecutiveWins}`)
		console.log(`Longest Loss Streak: ${performance.consecutiveLosses}`)
		console.log(`Trades per Hour: ${statistics.tradesPerHour.toFixed(2)}`)

		console.log('\n' + '-'.repeat(30) + ' TERMINATION ' + '-'.repeat(30))

		// Termination Info
		console.log(`Reason: ${termination.reason}`)
		console.log(`Message: ${termination.message}`)

		console.log('\n' + '='.repeat(60))
	}
}
