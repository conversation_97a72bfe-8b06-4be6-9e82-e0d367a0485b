import { ema } from '../indicators/ema'

export function evaluateEmaCross(candles: Candle[], config: EMAScalpingConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)

	const { fastPeriod, slowPeriod } = config // Recommended: 7, 14

	const fast = ema(closes, fastPeriod)
	const slow = ema(closes, slowPeriod)

	const i = closes.length - 1
	if (i < Math.max(fastPeriod, slowPeriod)) return { signal: undefined, reason: 'Not enough data', confidence: 'low' }

	const f = fast[i],
		s = slow[i]
	const pf = fast[i - 1],
		ps = slow[i - 1]

	if (f === undefined || s === undefined || pf === undefined || ps === undefined) {
		return { signal: undefined, reason: 'Incomplete EMA values', confidence: 'low' }
	}

	const strength = (Math.abs(f - s) / s) * 100

	let confidence: SignalMeta['confidence'] = 'low'
	if (strength > 0.1) confidence = 'medium'
	else if (strength > 0.2) confidence = 'high'

	// Golden cross
	if (pf <= ps && f > s) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `Fast EMA crossed above slow EMA (${strength.toFixed(3)}%)`,
				confidence
			}
		}
	}

	// Death cross
	if (pf >= ps && f < s) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `Fast EMA crossed below slow EMA (${strength.toFixed(3)}%)`,
				confidence
			}
		}
	}

	return { signal: 'HOLD', reason: 'No crossover or duplicate signal', confidence }
}
