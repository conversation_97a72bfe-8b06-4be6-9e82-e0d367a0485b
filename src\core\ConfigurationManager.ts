/**
 * Configuration Manager for Trading System
 * Handles loading, saving, and managing trading configurations
 */

import { EventEmitter } from 'events'
import {
	validateMoneyManagementConfig,
	getDefaultConfig,
	createValidatedConfig,
	calculateRecommendedConfig,
	exportConfig,
	importConfig,
	ValidationResult,
	DEFAULT_MONEY_MANAGEMENT_CONFIGS
} from '../utils/moneyManagementConfig'

export interface ConfigurationProfile {
	id: string
	name: string
	description: string
	moneyManagement: MoneyManagementConfig
	createdAt: number
	updatedAt: number
	isDefault?: boolean
}

export interface ConfigurationManagerOptions {
	autoSave?: boolean
	configDirectory?: string
	defaultProfile?: keyof typeof DEFAULT_MONEY_MANAGEMENT_CONFIGS
}

export class ConfigurationManager extends EventEmitter {
	private profiles: Map<string, ConfigurationProfile> = new Map()
	private activeProfile: string | null = null
	private options: ConfigurationManagerOptions

	constructor(options: ConfigurationManagerOptions = {}) {
		super()
		this.options = {
			autoSave: true,
			defaultProfile: 'moderate',
			...options
		}
		
		this.initializeDefaultProfiles()
	}

	/**
	 * Initialize default configuration profiles
	 */
	private initializeDefaultProfiles(): void {
		const profiles = [
			{
				id: 'conservative',
				name: 'Conservative Trading',
				description: 'Low risk, capital preservation focused',
				config: getDefaultConfig('conservative')
			},
			{
				id: 'moderate',
				name: 'Moderate Trading',
				description: 'Balanced risk and reward',
				config: getDefaultConfig('moderate')
			},
			{
				id: 'aggressive',
				name: 'Aggressive Trading',
				description: 'High risk, high reward potential',
				config: getDefaultConfig('aggressive')
			},
			{
				id: 'fibonacci',
				name: 'Fibonacci Strategy',
				description: 'Fibonacci-based martingale progression',
				config: getDefaultConfig('fibonacci')
			}
		]

		profiles.forEach(({ id, name, description, config }) => {
			const profile: ConfigurationProfile = {
				id,
				name,
				description,
				moneyManagement: config,
				createdAt: Date.now(),
				updatedAt: Date.now(),
				isDefault: true
			}
			this.profiles.set(id, profile)
		})

		// Set default active profile
		this.activeProfile = this.options.defaultProfile || 'moderate'
	}

	/**
	 * Create a new configuration profile
	 */
	createProfile(
		id: string,
		name: string,
		description: string,
		moneyManagement: Partial<MoneyManagementConfig>,
		baseProfile?: keyof typeof DEFAULT_MONEY_MANAGEMENT_CONFIGS
	): ValidationResult {
		if (this.profiles.has(id)) {
			throw new Error(`Profile with ID '${id}' already exists`)
		}

		const { config, validation } = createValidatedConfig(moneyManagement, baseProfile)

		if (validation.isValid) {
			const profile: ConfigurationProfile = {
				id,
				name,
				description,
				moneyManagement: config,
				createdAt: Date.now(),
				updatedAt: Date.now(),
				isDefault: false
			}

			this.profiles.set(id, profile)
			this.emit('profile:created', profile)

			if (this.options.autoSave) {
				this.saveProfile(id)
			}
		}

		return validation
	}

	/**
	 * Update an existing configuration profile
	 */
	updateProfile(id: string, updates: Partial<MoneyManagementConfig>): ValidationResult {
		const profile = this.profiles.get(id)
		if (!profile) {
			throw new Error(`Profile with ID '${id}' not found`)
		}

		if (profile.isDefault) {
			throw new Error('Cannot modify default profiles')
		}

		const updatedConfig = { ...profile.moneyManagement, ...updates }
		const validation = validateMoneyManagementConfig(updatedConfig)

		if (validation.isValid) {
			profile.moneyManagement = updatedConfig as MoneyManagementConfig
			profile.updatedAt = Date.now()
			
			this.emit('profile:updated', profile)

			if (this.options.autoSave) {
				this.saveProfile(id)
			}
		}

		return validation
	}

	/**
	 * Delete a configuration profile
	 */
	deleteProfile(id: string): boolean {
		const profile = this.profiles.get(id)
		if (!profile) {
			return false
		}

		if (profile.isDefault) {
			throw new Error('Cannot delete default profiles')
		}

		if (this.activeProfile === id) {
			this.activeProfile = this.options.defaultProfile || 'moderate'
		}

		this.profiles.delete(id)
		this.emit('profile:deleted', { id, profile })

		return true
	}

	/**
	 * Get a configuration profile
	 */
	getProfile(id: string): ConfigurationProfile | null {
		return this.profiles.get(id) || null
	}

	/**
	 * Get all configuration profiles
	 */
	getAllProfiles(): ConfigurationProfile[] {
		return Array.from(this.profiles.values())
	}

	/**
	 * Set active configuration profile
	 */
	setActiveProfile(id: string): boolean {
		if (!this.profiles.has(id)) {
			return false
		}

		const oldProfile = this.activeProfile
		this.activeProfile = id
		
		this.emit('profile:activated', {
			oldProfile,
			newProfile: id,
			config: this.getActiveConfig()
		})

		return true
	}

	/**
	 * Get active configuration profile
	 */
	getActiveProfile(): ConfigurationProfile | null {
		return this.activeProfile ? this.profiles.get(this.activeProfile) || null : null
	}

	/**
	 * Get active configuration
	 */
	getActiveConfig(): MoneyManagementConfig | null {
		const profile = this.getActiveProfile()
		return profile ? profile.moneyManagement : null
	}

	/**
	 * Create profile from account balance recommendation
	 */
	createRecommendedProfile(accountBalance: number, profileName?: string): string {
		const config = calculateRecommendedConfig(accountBalance)
		const id = `recommended_${Date.now()}`
		const name = profileName || `Recommended for $${accountBalance.toFixed(2)}`
		const description = `Auto-generated configuration based on account balance of $${accountBalance.toFixed(2)}`

		this.createProfile(id, name, description, config)
		return id
	}

	/**
	 * Validate current active configuration
	 */
	validateActiveConfig(): ValidationResult {
		const config = this.getActiveConfig()
		if (!config) {
			return {
				isValid: false,
				errors: [{ field: 'profile', value: null, message: 'No active profile set', rule: 'required' }],
				warnings: []
			}
		}

		return validateMoneyManagementConfig(config)
	}

	/**
	 * Export profile to JSON
	 */
	exportProfile(id: string): string {
		const profile = this.profiles.get(id)
		if (!profile) {
			throw new Error(`Profile with ID '${id}' not found`)
		}

		return JSON.stringify(profile, null, 2)
	}

	/**
	 * Import profile from JSON
	 */
	importProfile(jsonString: string, newId?: string): string {
		try {
			const importedProfile = JSON.parse(jsonString) as ConfigurationProfile
			
			// Validate the money management configuration
			const validation = validateMoneyManagementConfig(importedProfile.moneyManagement)
			if (!validation.isValid) {
				throw new Error(`Invalid configuration: ${validation.errors.map(e => e.message).join(', ')}`)
			}

			// Generate new ID if not provided or if it conflicts
			const id = newId || importedProfile.id
			const finalId = this.profiles.has(id) ? `${id}_imported_${Date.now()}` : id

			const profile: ConfigurationProfile = {
				...importedProfile,
				id: finalId,
				createdAt: Date.now(),
				updatedAt: Date.now(),
				isDefault: false
			}

			this.profiles.set(finalId, profile)
			this.emit('profile:imported', profile)

			return finalId
		} catch (error) {
			if (error instanceof SyntaxError) {
				throw new Error('Invalid JSON format')
			}
			throw error
		}
	}

	/**
	 * Save profile (placeholder for file system integration)
	 */
	private saveProfile(id: string): void {
		// This would integrate with file system or database
		// For now, just emit an event
		const profile = this.profiles.get(id)
		if (profile) {
			this.emit('profile:saved', profile)
		}
	}

	/**
	 * Get configuration summary
	 */
	getConfigurationSummary(): {
		totalProfiles: number
		activeProfile: string | null
		defaultProfiles: number
		customProfiles: number
		lastUpdated: number
	} {
		const profiles = Array.from(this.profiles.values())
		const defaultProfiles = profiles.filter(p => p.isDefault).length
		const customProfiles = profiles.filter(p => !p.isDefault).length
		const lastUpdated = Math.max(...profiles.map(p => p.updatedAt))

		return {
			totalProfiles: profiles.length,
			activeProfile: this.activeProfile,
			defaultProfiles,
			customProfiles,
			lastUpdated
		}
	}

	/**
	 * Reset to default configuration
	 */
	resetToDefaults(): void {
		// Remove custom profiles
		const customProfiles = Array.from(this.profiles.entries())
			.filter(([_, profile]) => !profile.isDefault)
			.map(([id]) => id)

		customProfiles.forEach(id => this.profiles.delete(id))

		// Reset active profile to default
		this.activeProfile = this.options.defaultProfile || 'moderate'

		this.emit('configuration:reset')
	}
}
